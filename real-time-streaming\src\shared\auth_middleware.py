"""
Updated authentication middleware for UUID-based user IDs
This file should replace auth_middleware.py after the migration is complete
"""

from functools import wraps
from flask import request, jsonify, current_app
import jwt
import os
import uuid
from dotenv import load_dotenv

load_dotenv()

def token_required(allowed_roles=None):
    def decorator(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            token = None
            
            # Check for token in headers
            if 'Authorization' in request.headers:
                auth_header = request.headers['Authorization']
                print(f"Auth header received: {auth_header[:50]}...")  # Debug log
                try:
                    token = auth_header.split(" ")[1]  # Bearer <token>
                except IndexError:
                    print("Invalid token format")  # Debug log
                    return jsonify({'message': 'Invalid token format'}), 401

            if not token:
                print("Token is missing")  # Debug log
                return jsonify({'message': 'Token is missing'}), 401
            
            try:
                # Decode the token
                data = jwt.decode(token, os.getenv('JWT_SECRET'), algorithms=['HS256'])
                current_user_id = data['user_id']
                current_user_role = data['role']
                current_username = data['username']

                # Validate UUID format for user_id (except for center_counselor which uses center_code)
                if current_user_role != 'center_counselor':
                    # Check if user_id is an integer (old token format)
                    if isinstance(current_user_id, int):
                        print(f"Detected old integer user_id: {current_user_id}. Please login again to get a new UUID-based token.")
                        return jsonify({'message': 'Token format outdated. Please login again.'}), 401

                    # Validate UUID format for string user_id
                    try:
                        uuid.UUID(current_user_id)  # Validate UUID format
                    except (ValueError, TypeError) as e:
                        print(f"Invalid UUID format for user_id: {current_user_id} (type: {type(current_user_id).__name__})")
                        return jsonify({'message': 'Invalid user ID format. Please login again.'}), 401

                print(f"Token decoded successfully: user_id={current_user_id}, role={current_user_role}")  # Debug log

                # Check if user role is allowed
                if allowed_roles and current_user_role not in allowed_roles:
                    print(f"Insufficient permissions: {current_user_role} not in {allowed_roles}")  # Debug log
                    return jsonify({'message': 'Insufficient permissions'}), 403
                
                # Pass user info to the route
                request.current_user = {
                    'id': current_user_id,
                    'role': current_user_role,
                    'username': current_username
                }
                
            except jwt.ExpiredSignatureError:
                print("Token has expired")  # Debug log
                return jsonify({'message': 'Token has expired'}), 401
            except jwt.InvalidTokenError as e:
                print(f"Invalid token: {e}")  # Debug log
                return jsonify({'message': 'Invalid token'}), 401
            
            return f(*args, **kwargs)
        return decorated
    return decorator

def generate_token(user_id, username, role):
    """Generate JWT token for user with UUID support"""
    import datetime
    
    # Ensure user_id is string format for JWT
    if isinstance(user_id, uuid.UUID):
        user_id = str(user_id)
    
    payload = {
        'user_id': user_id,
        'username': username,
        'role': role,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)
    }
    
    token = jwt.encode(payload, os.getenv('JWT_SECRET'), algorithm='HS256')
    return token

def validate_uuid(uuid_string):
    """Validate UUID string format"""
    try:
        uuid.UUID(uuid_string)
        return True
    except ValueError:
        return False

def get_user_role_by_id(user_id):
    """Helper function to get user role by UUID from various tables."""
    from shared.database import Database
    
    db = Database()
    
    # Validate UUID format first (except for center codes)
    if not validate_uuid(user_id):
        # Check if it's a center code
        center = db.execute_query_one("SELECT 'center_counselor' as role FROM centers WHERE center_code = %s", (user_id,))
        if center:
            return center['role']
        return None
    
    # Check in users table (Director)
    user = db.execute_query_one("SELECT role FROM users WHERE id = %s", (user_id,))
    if user:
        return user['role']

    # Check in students table
    student = db.execute_query_one("SELECT 'student' as role FROM students WHERE id = %s", (user_id,))
    if student:
        return student['role']

    # Check in parents table
    parent = db.execute_query_one("SELECT 'parent' as role FROM parents WHERE id = %s", (user_id,))
    if parent:
        return parent['role']

    # Check in faculty table
    faculty = db.execute_query_one("SELECT 'faculty' as role FROM faculty WHERE id = %s", (user_id,))
    if faculty:
        return faculty['role']

    # Check in kota_teachers table
    teacher = db.execute_query_one("SELECT 'kota_teacher' as role FROM kota_teachers WHERE id = %s", (user_id,))
    if teacher:
        return teacher['role']

    return None  # Role not found
