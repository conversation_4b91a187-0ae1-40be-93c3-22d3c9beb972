import bcrypt
import random
import string
from datetime import datetime

def hash_password(password):
    """Hash password using bcrypt"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password, hashed):
    """Verify password against hash"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def generate_center_code():
    """Generate unique center code"""
    letters = ''.join(random.choices(string.ascii_uppercase, k=1))
    numbers = ''.join(random.choices(string.digits, k=4))
    return f"M{letters}{numbers}"

def generate_student_username(first_name, course, dob):
    """Generate student username: FirstnameCourse-DOB"""
    if isinstance(dob, str):
        # Parse date string to extract day, month, year
        try:
            date_obj = datetime.strptime(dob, '%Y-%m-%d')
            dob_str = date_obj.strftime('%d%m%Y')
        except:
            dob_str = dob.replace('-', '')
    else:
        dob_str = dob.strftime('%d%m%Y')
    
    return f"{first_name.upper()}{course.upper()}{dob_str}"

def generate_parent_username(parent_first_name, student_dob):
    """Generate parent username: ParentFirstname-StudentDOB"""
    if isinstance(student_dob, str):
        try:
            date_obj = datetime.strptime(student_dob, '%Y-%m-%d')
            dob_str = date_obj.strftime('%d%m%Y')
        except:
            dob_str = student_dob.replace('-', '')
    else:
        dob_str = student_dob.strftime('%d%m%Y')
    
    return f"{parent_first_name.upper()}{dob_str}"

def generate_faculty_username(first_name, center_code):
    """Generate faculty username: Firstname-CenterCode"""
    return f"{first_name.upper()}{center_code}"

def format_dob_as_password(dob):
    """Format date of birth as password"""
    if isinstance(dob, str):
        try:
            date_obj = datetime.strptime(dob, '%Y-%m-%d')
            return date_obj.strftime('%d%m%Y')
        except:
            return dob.replace('-', '')
    else:
        return dob.strftime('%d%m%Y')
