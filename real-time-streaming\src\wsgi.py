#!/usr/bin/env python3
"""
WSGI entry point for production deployment with Gunicorn
Flask-SocketIO with eventlet worker configuration
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # Import the Flask app and SocketIO instance
    from app import app, socketio
    print("✅ Successfully imported app and socketio from app.py")

    # For Flask-SocketIO with Gunicorn eventlet worker
    # Use the Flask app as the WSGI application
    # SocketIO will automatically handle WebSocket upgrades
    application = app
    print("✅ Using Flask app as WSGI application")
    print("🔧 SocketIO will handle WebSocket upgrades automatically")

except ImportError as e:
    print(f"❌ Import error in wsgi.py: {e}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Python path: {sys.path}")
    print(f"Files in current directory: {os.listdir('.')}")
    raise
except Exception as e:
    print(f"❌ Error configuring WSGI application: {e}")
    raise

if __name__ == "__main__":
    # This is for development only
    socketio.run(app, host='0.0.0.0', port=8012, debug=False, allow_unsafe_werkzeug=True)
