# Gunicorn configuration for Flask-SocketIO production deployment

import os
import multiprocessing

# Server socket
bind = "0.0.0.0:8012"
backlog = 2048

# Worker processes
workers = 1  # Flask-SocketIO requires only 1 worker for proper WebSocket handling
worker_class = "eventlet"  # Required for SocketIO
worker_connections = 1000
timeout = 120  # Increased timeout for streaming
keepalive = 2

# Flask-SocketIO specific configuration
async_mode = "eventlet"
 
# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 50
 
# Logging
accesslog = "-"  # Log to stdout
errorlog = "-"   # Log to stderr
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
 
# Process naming
proc_name = 'realtime_streaming_service'
 
# Server mechanics
daemon = False
pidfile = '/tmp/realtime_streaming.pid'
user = None
group = None
tmp_upload_dir = None
 
# SSL (if needed)
# keyfile = '/path/to/keyfile'
# certfile = '/path/to/certfile'
 
# Environment
raw_env = [
    'FLASK_ENV=production',
]
 
# Preload app for better performance
preload_app = True
 
# Enable automatic worker restarts
reload = False
 
# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190
 
 